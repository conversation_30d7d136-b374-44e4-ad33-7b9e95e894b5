# app.py
import io
import json
import math
import sys
import time
import traceback
from contextlib import redirect_stdout, redirect_stderr
from typing import Any, Dict, List, Optional, Tuple, Union

import altair as alt
import numpy as np
import pandas as pd
import requests
import streamlit as st

# Notion 相关导入
try:
    from notion_client import Client
    NOTION_AVAILABLE = True
except ImportError:
    NOTION_AVAILABLE = False


# --------------------------
# Utilities
# --------------------------

def init_session_state():
    defaults = {
        "source_type": None,           # 'file' | 'api' | 'notion'
        "file_type": None,             # 'csv' | 'excel'
        "csv_sep": ",",
        "csv_encoding": "utf-8",
        "excel_sheet": None,
        "raw_df": None,
        "clean_df": None,
        "clean_code": "",
        "api_config": {
            "method": "GET",
            "url": "",
            "headers_json": "{}",
            "params_json": "{}",
            "body_json": "{}",
            "records_path": "",        # e.g. "data.items"
            "enable_pagination": False,
            "page_param": "page",
            "page_start": 1,
            "page_size_param": "page_size",
            "page_size_value": 100,
            "max_pages": 10,
            "auth_type": "None",       # None | Bearer | Basic
            "auth_bearer_token": "",
            "auth_basic_user": "",
            "auth_basic_pass": "",
            "timeout": 30,
        },
        "notion_config": {
            "token": "",
            "database_id": "",
            "filter_json": "{}",
            "sorts_json": "[]",
            "max_results": 100,
        },
        "viz_config": {
            "chart_type": "折线图",
            "x": None,
            "y": None,
            "color": None,
            "size": None,
            "tooltip": [],
            "aggregate": "none",       # none | sum | mean | median | count | min | max
            "sample_rows": 5000,
        },
    }
    for k, v in defaults.items():
        if k not in st.session_state:
            st.session_state[k] = v


def get_value_by_path(obj: Any, path: str) -> Any:
    """
    Traverse dict/list by a dot-separated path like "data.items".
    If path is empty, return obj as-is.
    """
    if not path:
        return obj
    cur = obj
    for part in path.split("."):
        if isinstance(cur, dict) and part in cur:
            cur = cur[part]
        else:
            # best effort: if dict but key missing, try the first list-like value
            return None
    return cur


def guess_records_in_json(j: Any) -> Optional[List[Dict]]:
    """
    Try to guess a list[dict] in a JSON object/array when no path provided.
    """
    if isinstance(j, list):
        # If it's a list of dicts
        if len(j) == 0:
            return []
        if isinstance(j[0], dict):
            return j
        # list of scalars -> wrap
        return [{"value": x} for x in j]

    if isinstance(j, dict):
        # Find first list-of-dicts
        for k, v in j.items():
            if isinstance(v, list):
                if len(v) == 0:
                    return []
                if isinstance(v[0], dict):
                    return v
        # If no list-of-dicts, just flatten dict to single row
        return [j]
    return None


def to_dataframe_from_json(j: Any, records_path: str = "") -> pd.DataFrame:
    target = get_value_by_path(j, records_path) if records_path else None
    if target is None:
        target = guess_records_in_json(j)

    # If still None, fallback
    if target is None:
        return pd.DataFrame()

    if isinstance(target, list):
        if len(target) == 0:
            return pd.DataFrame()
        if isinstance(target[0], dict):
            return pd.json_normalize(target, max_level=3)
        else:
            return pd.DataFrame({"value": target})
    elif isinstance(target, dict):
        return pd.json_normalize(target, max_level=3)
    else:
        return pd.DataFrame({"value": [target]})


def parse_json_input(s: str, fallback: dict) -> dict:
    s = (s or "").strip()
    if not s:
        return fallback
    try:
        return json.loads(s)
    except Exception:
        return fallback


def build_auth(api_cfg: dict):
    if api_cfg.get("auth_type") == "Bearer":
        token = api_cfg.get("auth_bearer_token", "").strip()
        if token:
            return None, {"Authorization": f"Bearer {token}"}
    elif api_cfg.get("auth_type") == "Basic":
        user = api_cfg.get("auth_basic_user", "")
        pw = api_cfg.get("auth_basic_pass", "")
        return (user, pw), {}
    return None, {}


def fetch_api_once(api_cfg: dict, page: Optional[int] = None) -> Tuple[pd.DataFrame, dict, int]:
    method = api_cfg.get("method", "GET").upper()
    url = api_cfg.get("url", "").strip()
    timeout = int(api_cfg.get("timeout", 30))
    records_path = api_cfg.get("records_path", "")

    headers = parse_json_input(api_cfg.get("headers_json", "{}"), {})
    params = parse_json_input(api_cfg.get("params_json", "{}"), {})
    body = parse_json_input(api_cfg.get("body_json", "{}"), {})

    auth_tuple, auth_hdr = build_auth(api_cfg)
    headers.update(auth_hdr)

    # Pagination param injection
    if page is not None:
        page_param = api_cfg.get("page_param") or "page"
        params[page_param] = page
        if api_cfg.get("page_size_param"):
            params[api_cfg["page_size_param"]] = api_cfg.get("page_size_value", 100)

    if method == "GET":
        resp = requests.get(url, headers=headers, params=params, timeout=timeout, auth=auth_tuple)
    elif method == "POST":
        resp = requests.post(url, headers=headers, params=params, json=body, timeout=timeout, auth=auth_tuple)
    else:
        raise ValueError(f"Unsupported method: {method}")

    resp.raise_for_status()
    try:
        j = resp.json()
    except Exception:
        # Not JSON, try CSV or text; build DataFrame
        content = resp.content.decode("utf-8", errors="ignore")
        try:
            return pd.read_csv(io.StringIO(content)), {}, resp.status_code
        except Exception:
            return pd.DataFrame({"raw": [content]}), {}, resp.status_code

    df = to_dataframe_from_json(j, records_path)
    return df, j, resp.status_code


def fetch_api_all(api_cfg: dict) -> Tuple[pd.DataFrame, List[dict]]:
    enable_pagination = bool(api_cfg.get("enable_pagination"))
    page_start = int(api_cfg.get("page_start", 1))
    max_pages = int(api_cfg.get("max_pages", 10))

    frames = []
    metas = []

    if not enable_pagination:
        df, meta, _ = fetch_api_once(api_cfg, page=None)
        frames.append(df)
        metas.append(meta)
    else:
        current = page_start
        for i in range(max_pages):
            df, meta, _ = fetch_api_once(api_cfg, page=current)
            metas.append(meta)
            if df is None or df.empty:
                break
            frames.append(df)
            current += 1

    if not frames:
        return pd.DataFrame(), metas
    return pd.concat(frames, ignore_index=True), metas


def fetch_notion_database(notion_cfg: dict) -> pd.DataFrame:
    """
    从 Notion 数据库获取数据
    """
    if not NOTION_AVAILABLE:
        raise ImportError("notion-client 未安装。请运行: pip install notion-client")

    token = notion_cfg.get("token", "").strip()
    database_id = notion_cfg.get("database_id", "").strip()

    if not token:
        raise ValueError("请提供 Notion Token")
    if not database_id:
        raise ValueError("请提供 Database ID")

    # 初始化 Notion 客户端
    notion = Client(auth=token)

    # 解析过滤器和排序
    filter_dict = parse_json_input(notion_cfg.get("filter_json", "{}"), {})
    sorts_list = parse_json_input(notion_cfg.get("sorts_json", "[]"), [])
    max_results = int(notion_cfg.get("max_results", 100))

    # 构建查询参数
    query_params = {
        "database_id": database_id,
        "page_size": min(max_results, 100)  # Notion API 限制每次最多100条
    }

    if filter_dict:
        query_params["filter"] = filter_dict
    if sorts_list:
        query_params["sorts"] = sorts_list

    # 获取数据
    all_results = []
    has_more = True
    start_cursor = None

    while has_more and len(all_results) < max_results:
        if start_cursor:
            query_params["start_cursor"] = start_cursor

        response = notion.databases.query(**query_params)
        all_results.extend(response["results"])

        has_more = response["has_more"]
        start_cursor = response.get("next_cursor")

        if len(all_results) >= max_results:
            all_results = all_results[:max_results]
            break

    # 转换为 DataFrame
    if not all_results:
        return pd.DataFrame()

    # 解析 Notion 页面数据
    rows = []
    for page in all_results:
        row = {"id": page["id"]}

        # 解析属性
        properties = page.get("properties", {})
        for prop_name, prop_data in properties.items():
            prop_type = prop_data.get("type")

            if prop_type == "title":
                titles = prop_data.get("title", [])
                row[prop_name] = "".join([t.get("plain_text", "") for t in titles])
            elif prop_type == "rich_text":
                texts = prop_data.get("rich_text", [])
                row[prop_name] = "".join([t.get("plain_text", "") for t in texts])
            elif prop_type == "number":
                row[prop_name] = prop_data.get("number")
            elif prop_type == "select":
                select_data = prop_data.get("select")
                row[prop_name] = select_data.get("name") if select_data else None
            elif prop_type == "multi_select":
                multi_select = prop_data.get("multi_select", [])
                row[prop_name] = ", ".join([ms.get("name", "") for ms in multi_select])
            elif prop_type == "date":
                date_data = prop_data.get("date")
                row[prop_name] = date_data.get("start") if date_data else None
            elif prop_type == "checkbox":
                row[prop_name] = prop_data.get("checkbox", False)
            elif prop_type == "url":
                row[prop_name] = prop_data.get("url")
            elif prop_type == "email":
                row[prop_name] = prop_data.get("email")
            elif prop_type == "phone_number":
                row[prop_name] = prop_data.get("phone_number")
            elif prop_type == "people":
                people = prop_data.get("people", [])
                row[prop_name] = ", ".join([p.get("name", "") for p in people])
            elif prop_type == "files":
                files = prop_data.get("files", [])
                row[prop_name] = ", ".join([f.get("name", "") for f in files])
            elif prop_type == "relation":
                relations = prop_data.get("relation", [])
                row[prop_name] = ", ".join([r.get("id", "") for r in relations])
            elif prop_type == "formula":
                formula = prop_data.get("formula", {})
                formula_type = formula.get("type")
                if formula_type == "string":
                    row[prop_name] = formula.get("string")
                elif formula_type == "number":
                    row[prop_name] = formula.get("number")
                elif formula_type == "boolean":
                    row[prop_name] = formula.get("boolean")
                elif formula_type == "date":
                    date_data = formula.get("date")
                    row[prop_name] = date_data.get("start") if date_data else None
                else:
                    row[prop_name] = str(formula)
            else:
                # 其他类型，尝试转换为字符串
                row[prop_name] = str(prop_data.get(prop_type, ""))

        rows.append(row)

    return pd.DataFrame(rows)


def apply_clean_code(df: pd.DataFrame, code: str) -> Tuple[pd.DataFrame, str, Optional[str]]:
    """
    Execute user code with 'df' available.
    Accepted outputs:
      - modify df in-place and leave as 'df'
      - or assign to variable 'result' or 'clean_df'
    Returns: (new_df, logs, error_message)
    """
    local_env: Dict[str, Any] = {}
    # Provide safe, limited namespace (still dangerous if you run untrusted code)
    allowed_globals = {
        "__builtins__": {
            "abs": abs,
            "all": all,
            "any": any,
            "len": len,
            "min": min,
            "max": max,
            "sum": sum,
            "range": range,
            "enumerate": enumerate,
            "zip": zip,
            "map": map,
            "filter": filter,
            "sorted": sorted,
            "print": print,
        },
        "pd": pd,
        "np": np,
        "math": math,
        "json": json,
    }
    local_env["df"] = df.copy(deep=False)

    stdout_buf = io.StringIO()
    stderr_buf = io.StringIO()
    try:
        with redirect_stdout(stdout_buf), redirect_stderr(stderr_buf):
            exec(code, allowed_globals, local_env)
        # Collect outputs
        out_df = None
        if isinstance(local_env.get("result"), pd.DataFrame):
            out_df = local_env["result"]
        elif isinstance(local_env.get("clean_df"), pd.DataFrame):
            out_df = local_env["clean_df"]
        elif isinstance(local_env.get("df"), pd.DataFrame):
            out_df = local_env["df"]
        else:
            return df, stdout_buf.getvalue(), "未找到 DataFrame 结果（期望变量名为 df / result / clean_df）"

        logs = stdout_buf.getvalue()
        err = stderr_buf.getvalue()
        if err:
            logs += "\n[stderr]\n" + err

        # Ensure DataFrame
        if not isinstance(out_df, pd.DataFrame):
            return df, logs, "执行完成，但结果不是 DataFrame"
        return out_df, logs, None
    except Exception as e:
        tb = traceback.format_exc()
        logs = stdout_buf.getvalue()
        return df, logs, f"执行错误: {e}\n{tb}"


def altair_chart_from_config(df: pd.DataFrame, cfg: dict) -> alt.Chart:
    chart_type = cfg.get("chart_type", "折线图")
    x = cfg.get("x")
    y = cfg.get("y")
    color = cfg.get("color")
    size = cfg.get("size")
    tooltip = cfg.get("tooltip", [])
    aggregate = cfg.get("aggregate", "none")
    sample_rows = int(cfg.get("sample_rows", 5000))

    if df is None or df.empty:
        return alt.Chart(pd.DataFrame({"提示": ["暂无数据"]})).mark_text().encode(text="提示")

    # Sampling to keep charts responsive
    if sample_rows and len(df) > sample_rows:
        df_plot = df.sample(n=sample_rows, random_state=42)
    else:
        df_plot = df

    # Infer default x/y
    cols = list(df_plot.columns)
    # choose first column for x, second for y
    if not x and cols:
        x = cols[0]
    if not y and len(cols) > 1:
        y = cols[1]

    def aggregate_field(field):
        if aggregate and aggregate != "none":
            return alt.Y(f"{aggregate}({field})") if field == y else alt.X(f"{aggregate}({field})")
        return field

    enc = {}
    if x:
        enc["x"] = alt.X(x)
    if y:
        enc["y"] = alt.Y(y)
    if color:
        enc["color"] = alt.Color(color)
    if size:
        enc["size"] = alt.Size(size)
    if tooltip:
        enc["tooltip"] = tooltip

    if chart_type == "折线图":
        ch = alt.Chart(df_plot).mark_line(point=True).encode(**enc)
    elif chart_type == "柱状图":
        # aggregate y if specified
        if aggregate and aggregate != "none" and x and y:
            ch = alt.Chart(df_plot).mark_bar().encode(
                x=alt.X(x),
                y=alt.Y(f"{aggregate}({y})"),
                color=alt.Color(color) if color else alt.value("#4C78A8"),
                tooltip=tooltip if tooltip else [x, y],
            )
        else:
            ch = alt.Chart(df_plot).mark_bar().encode(**enc)
    elif chart_type == "散点图":
        ch = alt.Chart(df_plot).mark_circle(opacity=0.7).encode(**enc)
    elif chart_type == "直方图":
        if x:
            ch = alt.Chart(df_plot).mark_bar().encode(x=alt.X(x, bin=True), y="count()", color=alt.Color(color) if color else alt.value("#72B7B2"))
        else:
            ch = alt.Chart(df_plot).mark_text().encode(text=alt.value("请选择用于直方图的数值列"))
    elif chart_type == "箱线图":
        if x and y:
            ch = alt.Chart(df_plot).mark_boxplot().encode(x=x, y=y, color=alt.Color(color) if color else alt.value("#F58518"))
        elif y:
            ch = alt.Chart(df_plot).mark_boxplot().encode(y=y, color=alt.Color(color) if color else alt.value("#F58518"))
        else:
            ch = alt.Chart(df_plot).mark_text().encode(text=alt.value("请选择用于箱线图的列"))
    else:
        ch = alt.Chart(df_plot).mark_point().encode(**enc)

    return ch.interactive()


def df_to_excel_bytes(df: pd.DataFrame) -> bytes:
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, index=False, sheet_name="data")
    return output.getvalue()


# --------------------------
# Streamlit App
# --------------------------

st.set_page_config(page_title="数据分析工作台", page_icon="📊", layout="wide")
init_session_state()

st.title("📊 数据分析工作台")

# API quick update button in header
col_l, col_r = st.columns([1, 2], vertical_alignment="center")
with col_l:
    st.caption("支持 CSV、Excel、API、Notion 数据导入，代码清洗与可视化")
with col_r:
    source_type = st.session_state.get("source_type")
    if source_type == "api":
        if st.button("🔄 一键更新（API）", use_container_width=True):
            with st.spinner("正在从 API 拉取数据并应用清洗..."):
                try:
                    df_raw, _metas = fetch_api_all(st.session_state["api_config"])
                    st.session_state["raw_df"] = df_raw
                    # Re-apply cleaning if any
                    code = st.session_state.get("clean_code", "") or ""
                    if code.strip():
                        df_clean, logs, err = apply_clean_code(df_raw, code)
                        if err:
                            st.error(f"清洗代码执行失败：{err}")
                            if logs.strip():
                                with st.expander("查看执行日志"):
                                    st.code(logs)
                        else:
                            st.session_state["clean_df"] = df_clean
                            st.success(f"更新完成：{len(df_clean):,} 行")
                    else:
                        st.session_state["clean_df"] = df_raw.copy()
                        st.success(f"更新完成：{len(df_raw):,} 行（未设置清洗代码）")
                except Exception as e:
                    st.error(f"API 更新失败：{e}")
    elif source_type == "notion" and NOTION_AVAILABLE:
        if st.button("🔄 一键更新（Notion）", use_container_width=True):
            with st.spinner("正在从 Notion 拉取数据并应用清洗..."):
                try:
                    df_raw = fetch_notion_database(st.session_state["notion_config"])
                    st.session_state["raw_df"] = df_raw
                    # Re-apply cleaning if any
                    code = st.session_state.get("clean_code", "") or ""
                    if code.strip():
                        df_clean, logs, err = apply_clean_code(df_raw, code)
                        if err:
                            st.error(f"清洗代码执行失败：{err}")
                            if logs.strip():
                                with st.expander("查看执行日志"):
                                    st.code(logs)
                        else:
                            st.session_state["clean_df"] = df_clean
                            st.success(f"更新完成：{len(df_clean):,} 行")
                    else:
                        st.session_state["clean_df"] = df_raw.copy()
                        st.success(f"更新完成：{len(df_raw):,} 行（未设置清洗代码）")
                except Exception as e:
                    st.error(f"Notion 更新失败：{e}")

tabs = st.tabs(["数据导入", "设置/清洗", "可视化", "配置管理"])

# --------------------------
# Tab 1: 数据导入
# --------------------------
with tabs[0]:
    st.subheader("数据导入")

    # 根据 Notion 是否可用决定选项
    if NOTION_AVAILABLE:
        source_options = ["本地文件（CSV/Excel）", "CRM/API", "Notion 数据库"]
    else:
        source_options = ["本地文件（CSV/Excel）", "CRM/API"]

    source_type = st.radio("选择数据源", options=source_options, horizontal=True)

    if not NOTION_AVAILABLE and "Notion" in str(source_type):
        st.error("❌ Notion 功能不可用。请安装 notion-client: `pip install notion-client`")

    if source_type.startswith("本地文件"):
        st.session_state["source_type"] = "file"
        file = st.file_uploader("上传 CSV 或 Excel 文件", type=["csv", "xlsx", "xls"])
        if file is not None:
            file_name = file.name.lower()
            if file_name.endswith(".csv"):
                st.session_state["file_type"] = "csv"
                st.session_state["excel_sheet"] = None
                c1, c2 = st.columns(2)
                with c1:
                    st.session_state["csv_sep"] = st.text_input("CSV 分隔符", value=st.session_state.get("csv_sep", ","), max_chars=3)
                with c2:
                    st.session_state["csv_encoding"] = st.text_input("CSV 编码", value=st.session_state.get("csv_encoding", "utf-8"))
                try:
                    # 首先尝试用户指定的编码
                    df = pd.read_csv(file, sep=st.session_state["csv_sep"], encoding=st.session_state["csv_encoding"])
                    st.session_state["raw_df"] = df
                    st.success(f"CSV 导入成功：{len(df):,} 行，{len(df.columns)} 列")
                    st.dataframe(df.head(50), use_container_width=True)
                except UnicodeDecodeError as e:
                    # 如果编码失败，尝试常见的中文编码
                    encodings_to_try = ['gbk', 'gb2312', 'utf-8-sig', 'cp936', 'latin1']
                    success = False
                    
                    for encoding in encodings_to_try:
                        if encoding == st.session_state["csv_encoding"]:
                            continue  # 跳过已经尝试过的编码
                        try:
                            file.seek(0)  # 重置文件指针
                            df = pd.read_csv(file, sep=st.session_state["csv_sep"], encoding=encoding)
                            st.session_state["raw_df"] = df
                            st.session_state["csv_encoding"] = encoding  # 更新成功的编码
                            st.success(f"CSV 导入成功（自动检测编码为 {encoding}）：{len(df):,} 行，{len(df.columns)} 列")
                            st.dataframe(df.head(50), use_container_width=True)
                            success = True
                            break
                        except Exception:
                            continue
                    
                    if not success:
                        st.error(f"读取 CSV 失败：{e}\n\n请尝试手动指定正确的编码格式（如：gbk、gb2312、utf-8-sig 等）")
                except Exception as e:
                    st.error(f"读取 CSV 失败：{e}")
            else:
                st.session_state["file_type"] = "excel"
                xls = pd.ExcelFile(file)
                sheet = st.selectbox("选择工作表", options=xls.sheet_names)
                st.session_state["excel_sheet"] = sheet
                try:
                    df = pd.read_excel(file, sheet_name=sheet)
                    st.session_state["raw_df"] = df
                    st.success(f"Excel 导入成功：{len(df):,} 行，{len(df.columns)} 列（工作表：{sheet}）")
                    st.dataframe(df.head(50), use_container_width=True)
                except Exception as e:
                    st.error(f"读取 Excel 失败：{e}")

    elif source_type.startswith("CRM/API"):
        st.session_state["source_type"] = "api"
        api_cfg = st.session_state["api_config"]

        with st.container(border=True):
            st.markdown("#### 基础配置")
            c1, c2, c3 = st.columns([2, 1, 1])
            with c1:
                api_cfg["url"] = st.text_input("URL", value=api_cfg.get("url", ""), placeholder="https://example.com/api/endpoint ")
            with c2:
                api_cfg["method"] = st.selectbox("HTTP 方法", options=["GET", "POST"], index=0 if api_cfg.get("method", "GET") == "GET" else 1)
            with c3:
                api_cfg["timeout"] = st.number_input("超时（秒）", min_value=1, max_value=300, value=int(api_cfg.get("timeout", 30)))

            c4, c5 = st.columns(2)
            with c4:
                api_cfg["auth_type"] = st.selectbox("认证方式", ["None", "Bearer", "Basic"], index=["None", "Bearer", "Basic"].index(api_cfg.get("auth_type", "None")))
                if api_cfg["auth_type"] == "Bearer":
                    api_cfg["auth_bearer_token"] = st.text_input("Bearer Token", type="password", value=api_cfg.get("auth_bearer_token", ""))
                elif api_cfg["auth_type"] == "Basic":
                    api_cfg["auth_basic_user"] = st.text_input("用户名", value=api_cfg.get("auth_basic_user", ""))
                    api_cfg["auth_basic_pass"] = st.text_input("密码", type="password", value=api_cfg.get("auth_basic_pass", ""))
            with c5:
                api_cfg["records_path"] = st.text_input("数据路径（可选，点号分隔）", value=api_cfg.get("records_path", ""), placeholder="例如 data.items")

        with st.container(border=True):
            st.markdown("#### 请求细节（JSON 形式）")
            c6, c7 = st.columns(2)
            with c6:
                api_cfg["headers_json"] = st.text_area("Headers JSON", value=api_cfg.get("headers_json", "{}"), height=120)
                api_cfg["params_json"] = st.text_area("Query Params JSON", value=api_cfg.get("params_json", "{}"), height=120)
            with c7:
                if api_cfg.get("method", "GET") == "POST":
                    api_cfg["body_json"] = st.text_area("Body JSON（POST）", value=api_cfg.get("body_json", "{}"), height=245)
                else:
                    st.caption("GET 请求无需 Body")

        with st.container(border=True):
            st.markdown("#### 分页（可选）")
            api_cfg["enable_pagination"] = st.checkbox("启用分页", value=bool(api_cfg.get("enable_pagination", False)))
            if api_cfg["enable_pagination"]:
                c8, c9, c10 = st.columns(3)
                with c8:
                    api_cfg["page_param"] = st.text_input("页码参数名", value=api_cfg.get("page_param", "page"))
                    api_cfg["page_start"] = st.number_input("起始页码", value=int(api_cfg.get("page_start", 1)), step=1)
                with c9:
                    api_cfg["page_size_param"] = st.text_input("每页大小参数名", value=api_cfg.get("page_size_param", "page_size"))
                    api_cfg["page_size_value"] = st.number_input("每页大小", value=int(api_cfg.get("page_size_value", 100)), step=1)
                with c10:
                    api_cfg["max_pages"] = st.number_input("最大页数", value=int(api_cfg.get("max_pages", 10)), min_value=1, step=1)

        if st.button("拉取数据", type="primary"):
            with st.spinner("请求 API 中..."):
                try:
                    df_raw, metas = fetch_api_all(api_cfg)
                    st.session_state["raw_df"] = df_raw
                    st.success(f"API 导入成功：{len(df_raw):,} 行，{len(df_raw.columns)} 列")
                    st.dataframe(df_raw.head(50), use_container_width=True)
                except Exception as e:
                    st.error(f"API 请求失败：{e}")

    elif source_type.startswith("Notion") and NOTION_AVAILABLE:
        st.session_state["source_type"] = "notion"
        notion_cfg = st.session_state["notion_config"]

        with st.container(border=True):
            st.markdown("#### 🗃️ Notion 数据库配置")

            col1, col2 = st.columns([2, 1])
            with col1:
                notion_cfg["token"] = st.text_input(
                    "Notion Token",
                    value=notion_cfg.get("token", ""),
                    type="password",
                    placeholder="ntn_...",
                    help="在 Notion 设置中创建集成并获取 Token"
                )
            with col2:
                notion_cfg["max_results"] = st.number_input(
                    "最大结果数",
                    min_value=1,
                    max_value=1000,
                    value=int(notion_cfg.get("max_results", 100)),
                    step=10
                )

            notion_cfg["database_id"] = st.text_input(
                "Database ID",
                value=notion_cfg.get("database_id", ""),
                placeholder="例如：a8aec43384f447ed84390e8e42c2e089",
                help="数据库 URL 中的 32 位字符串，例如：https://notion.so/myworkspace/a8aec43384f447ed84390e8e42c2e089?v=..."
            )

            # Database ID 格式验证
            db_id = notion_cfg.get("database_id", "").strip()
            if db_id:
                # 移除可能的连字符和空格
                clean_db_id = db_id.replace("-", "").replace(" ", "")
                if len(clean_db_id) == 32 and clean_db_id.isalnum():
                    st.success("✅ Database ID 格式正确")
                    notion_cfg["database_id"] = clean_db_id  # 保存清理后的ID
                else:
                    st.error("❌ Database ID 格式不正确。应该是32位字符串（字母和数字）")
                    st.info("💡 提示：从 Notion 数据库 URL 中复制完整的 ID，例如：a8aec43384f447ed84390e8e42c2e089")

            # URL 解析工具
            with st.expander("🔧 从 URL 提取 Database ID", expanded=False):
                st.markdown("如果你有完整的 Notion 数据库 URL，可以在这里提取 Database ID：")
                notion_url = st.text_input(
                    "粘贴 Notion 数据库 URL",
                    placeholder="https://www.notion.so/workspace/a8aec43384f447ed84390e8e42c2e089?v=...",
                    key="notion_url_input"
                )

                if notion_url:
                    import re
                    # 匹配 32 位的字符串（可能包含连字符）
                    pattern = r'([a-f0-9]{8}-?[a-f0-9]{4}-?[a-f0-9]{4}-?[a-f0-9]{4}-?[a-f0-9]{12}|[a-f0-9]{32})'
                    match = re.search(pattern, notion_url, re.IGNORECASE)

                    if match:
                        extracted_id = match.group(1).replace("-", "")
                        st.success(f"✅ 提取到 Database ID: `{extracted_id}`")
                        if st.button("📋 使用此 ID", key="use_extracted_id"):
                            notion_cfg["database_id"] = extracted_id
                            st.success("Database ID 已设置！")
                            st.rerun()
                    else:
                        st.warning("⚠️ 未能从 URL 中提取到有效的 Database ID")

            # 预设 Token（如果提供）
            if not notion_cfg.get("token") and "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":
                if st.button("🔑 使用预设 Token"):
                    notion_cfg["token"] = "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"
                    st.success("已设置预设 Token")
                    st.rerun()

        with st.container(border=True):
            st.markdown("#### 🔍 查询配置（可选）")

            col3, col4 = st.columns(2)
            with col3:
                notion_cfg["filter_json"] = st.text_area(
                    "过滤器 JSON",
                    value=notion_cfg.get("filter_json", "{}"),
                    height=120,
                    placeholder='{"property": "Status", "select": {"equals": "Done"}}',
                    help="Notion API 过滤器格式"
                )
            with col4:
                notion_cfg["sorts_json"] = st.text_area(
                    "排序 JSON",
                    value=notion_cfg.get("sorts_json", "[]"),
                    height=120,
                    placeholder='[{"property": "Created", "direction": "descending"}]',
                    help="Notion API 排序格式"
                )

            with st.expander("📖 查看 Notion API 文档示例", expanded=False):
                st.markdown("""
                **过滤器示例：**
                ```json
                {
                  "and": [
                    {"property": "Status", "select": {"equals": "Done"}},
                    {"property": "Priority", "number": {"greater_than": 1}}
                  ]
                }
                ```

                **排序示例：**
                ```json
                [
                  {"property": "Created", "direction": "descending"},
                  {"property": "Name", "direction": "ascending"}
                ]
                ```
                """)

        if st.button("📊 获取 Notion 数据", type="primary"):
            with st.spinner("正在从 Notion 获取数据..."):
                try:
                    df_raw = fetch_notion_database(notion_cfg)
                    st.session_state["raw_df"] = df_raw
                    st.success(f"Notion 导入成功：{len(df_raw):,} 行，{len(df_raw.columns)} 列")
                    st.dataframe(df_raw.head(50), use_container_width=True)
                except Exception as e:
                    st.error(f"Notion 数据获取失败：{e}")
                    if "Unauthorized" in str(e):
                        st.error("请检查 Token 是否正确，以及是否已将集成添加到数据库")
                    elif "not found" in str(e):
                        st.error("请检查 Database ID 是否正确")

        # CSV 编码转换功能（仅对 CSV 文件显示）
        if (st.session_state.get("source_type") == "file" and
            st.session_state.get("file_type") == "csv" and
            isinstance(st.session_state.get("raw_df"), pd.DataFrame)):

            with st.expander("🔧 CSV 编码转换工具", expanded=False):
                st.markdown("如果你的CSV文件编码有问题，可以使用此工具转换为UTF-8格式：")

                col1, col2 = st.columns([2, 1])
                with col1:
                    new_filename = st.text_input("转换后的文件名", value="转换后的数据.csv")
                with col2:
                    if st.button("💾 转换并下载", type="primary"):
                        try:
                            # 将当前数据保存为UTF-8编码的CSV
                            csv_data = st.session_state["raw_df"].to_csv(index=False, encoding='utf-8')
                            st.download_button(
                                label="📥 下载转换后的文件",
                                data=csv_data,
                                file_name=new_filename,
                                mime="text/csv",
                                use_container_width=True
                            )
                            st.success("转换完成！点击上方按钮下载文件。")
                        except Exception as e:
                            st.error(f"转换失败：{e}")

# --------------------------
# Tab 2: 设置/清洗
# --------------------------
with tabs[1]:
    st.subheader("设置/清洗")

    if st.session_state.get("raw_df") is None:
        st.info("请先在“数据导入”页导入数据。")
    else:
        # 常用数据清洗按钮区域
        st.markdown("#### 🛠️ 常用数据清洗操作")

        # 获取当前数据（优先使用清洗后的数据）
        clean_df = st.session_state.get("clean_df")
        raw_df = st.session_state.get("raw_df")

        if clean_df is not None and not clean_df.empty:
            current_df = clean_df
        elif raw_df is not None and not raw_df.empty:
            current_df = raw_df
        else:
            current_df = None

        if current_df is not None and not current_df.empty:
            cols = list(current_df.columns)

            with st.container(border=True):
                st.markdown("##### 列操作")
                col1, col2, col3 = st.columns(3)

                with col1:
                    # 删除空格操作
                    st.markdown("**删除空格**")
                    selected_col_trim = st.selectbox("选择列（删除空格）", options=["请选择"] + cols, key="trim_col")
                    trim_type = st.radio("删除类型", ["前后空格", "所有空格", "仅前空格", "仅后空格"], key="trim_type", horizontal=True)

                    if st.button("🧹 执行删除空格", key="btn_trim"):
                        if selected_col_trim != "请选择":
                            try:
                                df_work = current_df.copy()
                                if trim_type == "前后空格":
                                    df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.strip()
                                elif trim_type == "所有空格":
                                    df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.replace(' ', '', regex=False)
                                elif trim_type == "仅前空格":
                                    df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.lstrip()
                                elif trim_type == "仅后空格":
                                    df_work[selected_col_trim] = df_work[selected_col_trim].astype(str).str.rstrip()

                                st.session_state["clean_df"] = df_work
                                st.success(f"已对列 '{selected_col_trim}' 执行{trim_type}操作")
                            except Exception as e:
                                st.error(f"操作失败：{e}")
                        else:
                            st.warning("请先选择要操作的列")

                with col2:
                    # 删除重复行
                    st.markdown("**删除重复行**")
                    duplicate_cols = st.multiselect("基于哪些列判断重复（空=全部列）", options=cols, key="dup_cols")
                    keep_option = st.selectbox("保留", ["第一个", "最后一个"], key="keep_dup")

                    if st.button("🗑️ 删除重复行", key="btn_dedup"):
                        try:
                            df_work = current_df.copy()
                            subset_cols = duplicate_cols if duplicate_cols else None
                            keep_val = "first" if keep_option == "第一个" else "last"

                            original_count = len(df_work)
                            df_work = df_work.drop_duplicates(subset=subset_cols, keep=keep_val)
                            removed_count = original_count - len(df_work)

                            st.session_state["clean_df"] = df_work
                            st.success(f"删除了 {removed_count} 行重复数据，剩余 {len(df_work)} 行")
                        except Exception as e:
                            st.error(f"操作失败：{e}")

                with col3:
                    # 删除空值行
                    st.markdown("**删除空值行**")
                    null_cols = st.multiselect("基于哪些列判断空值（空=任意列）", options=cols, key="null_cols")
                    null_how = st.selectbox("删除条件", ["任意列为空", "所有列都为空"], key="null_how")

                    if st.button("🚫 删除空值行", key="btn_dropna"):
                        try:
                            df_work = current_df.copy()
                            subset_cols = null_cols if null_cols else None
                            how_val = "any" if null_how == "任意列为空" else "all"

                            original_count = len(df_work)
                            df_work = df_work.dropna(subset=subset_cols, how=how_val)
                            removed_count = original_count - len(df_work)

                            st.session_state["clean_df"] = df_work
                            st.success(f"删除了 {removed_count} 行空值数据，剩余 {len(df_work)} 行")
                        except Exception as e:
                            st.error(f"操作失败：{e}")

            with st.container(border=True):
                st.markdown("##### 数据类型转换")
                col1, col2 = st.columns(2)

                with col1:
                    # 数据类型转换
                    st.markdown("**列类型转换**")
                    convert_col = st.selectbox("选择列", options=["请选择"] + cols, key="convert_col")
                    target_type = st.selectbox("目标类型", ["文本(str)", "整数(int)", "小数(float)", "日期(datetime)"], key="target_type")

                    if st.button("🔄 转换类型", key="btn_convert"):
                        if convert_col != "请选择":
                            try:
                                df_work = current_df.copy()
                                if target_type == "文本(str)":
                                    df_work[convert_col] = df_work[convert_col].astype(str)
                                elif target_type == "整数(int)":
                                    df_work[convert_col] = pd.to_numeric(df_work[convert_col], errors='coerce').astype('Int64')
                                elif target_type == "小数(float)":
                                    df_work[convert_col] = pd.to_numeric(df_work[convert_col], errors='coerce')
                                elif target_type == "日期(datetime)":
                                    df_work[convert_col] = pd.to_datetime(df_work[convert_col], errors='coerce')

                                st.session_state["clean_df"] = df_work
                                st.success(f"已将列 '{convert_col}' 转换为 {target_type}")
                            except Exception as e:
                                st.error(f"转换失败：{e}")
                        else:
                            st.warning("请先选择要转换的列")

                with col2:
                    # 列重命名
                    st.markdown("**列重命名**")
                    rename_col = st.selectbox("选择要重命名的列", options=["请选择"] + cols, key="rename_col")
                    new_name = st.text_input("新列名", key="new_col_name")

                    if st.button("📝 重命名列", key="btn_rename"):
                        if rename_col != "请选择" and new_name.strip():
                            try:
                                df_work = current_df.copy()
                                df_work = df_work.rename(columns={rename_col: new_name.strip()})
                                st.session_state["clean_df"] = df_work
                                st.success(f"已将列 '{rename_col}' 重命名为 '{new_name.strip()}'")
                            except Exception as e:
                                st.error(f"重命名失败：{e}")
                        else:
                            st.warning("请选择列并输入新列名")

            # 用户自定义清洗按钮区域
            with st.container(border=True):
                st.markdown("##### 🎛️ 自定义清洗操作")
                st.markdown("你可以添加自己的清洗按钮（功能开发中...）")

                # 预留给用户自定义按钮的区域
                custom_col1, custom_col2, custom_col3 = st.columns(3)
                with custom_col1:
                    if st.button("自定义操作1", disabled=True):
                        st.info("此功能正在开发中")
                with custom_col2:
                    if st.button("自定义操作2", disabled=True):
                        st.info("此功能正在开发中")
                with custom_col3:
                    if st.button("自定义操作3", disabled=True):
                        st.info("此功能正在开发中")

        st.markdown("---")
        st.markdown("#### 📝 高级清洗（代码模式）")
        st.markdown("你可以在下面输入自定义 pandas 清洗代码。")
        with st.expander("查看示例清洗代码", expanded=False):
            st.code(
                """# 示例1：保留列、重命名、类型转换
# df 为输入的 DataFrame，写法1：直接在 df 上修改
# df = df[['日期','渠道','订单金额']].rename(columns={'订单金额':'sales'}).copy()
# df['日期'] = pd.to_datetime(df['日期'])
# df['month'] = df['日期'].dt.to_period('M').astype(str)

# 示例2：写函数并返回 result
# def transform(df):
#     out = df.copy()
#     out = out.dropna(subset=['订单金额'])
#     out['订单金额'] = out['订单金额'].astype(float)
#     return out
# result = transform(df)

# 示例3：分组汇总
# clean_df = df.groupby('渠道', as_index=False)['订单金额'].sum()
""",
                language="python"
            )

        st.session_state["clean_code"] = st.text_area(
            "在此输入清洗代码（我会用 exec 执行）",
            value=st.session_state.get("clean_code", ""),
            height=260,
            placeholder="示例：df = df.dropna().rename(columns={'old':'new'})"
        )

        run_clean = st.button("运行清洗代码", type="primary")
        if run_clean:
            with st.spinner("执行清洗代码..."):
                df_in = st.session_state["raw_df"]
                new_df, logs, err = apply_clean_code(df_in, st.session_state["clean_code"])
                if err:
                    st.error(err)
                else:
                    st.session_state["clean_df"] = new_df
                    st.success(f"清洗成功：{len(new_df):,} 行，{len(new_df.columns)} 列")
                if logs and logs.strip():
                    with st.expander("查看执行日志"):
                        st.code(logs)

        # Preview
        c1, c2 = st.columns(2)
        with c1:
            st.markdown("##### 原始数据预览")
            st.dataframe(st.session_state["raw_df"].head(50), use_container_width=True)
        with c2:
            st.markdown("##### 清洗结果预览")
            if st.session_state.get("clean_df") is not None:
                st.dataframe(st.session_state["clean_df"].head(50), use_container_width=True)
            else:
                st.info("尚未生成清洗结果。点击“运行清洗代码”执行。")

        st.caption("安全提示：本功能会执行你输入的 Python 代码，请谨慎使用并确保代码来源可信。")

# --------------------------
# Tab 3: 可视化
# --------------------------
with tabs[2]:
    st.subheader("可视化")

    # 获取活跃的数据框（优先使用清洗后的数据）
    clean_df = st.session_state.get("clean_df")
    raw_df = st.session_state.get("raw_df")

    if clean_df is not None and not clean_df.empty:
        active_df = clean_df
    elif raw_df is not None and not raw_df.empty:
        active_df = raw_df
    else:
        active_df = None
    if active_df is None or active_df.empty:
        st.info("没有可视化的数据。请先导入并（可选）清洗。")
    else:
        df = active_df
        cols = list(df.columns)

        cfg = st.session_state["viz_config"]

        left, right = st.columns([1, 2])
        with left:
            cfg["chart_type"] = st.selectbox("图表类型", options=["折线图", "柱状图", "散点图", "直方图", "箱线图"], index=["折线图","柱状图","散点图","直方图","箱线图"].index(cfg.get("chart_type", "折线图")))
            cfg["x"] = st.selectbox("X 轴", options=[None] + cols, index=(cols.index(cfg.get("x")) + 1) if cfg.get("x") in cols else 0)
            cfg["y"] = st.selectbox("Y 轴", options=[None] + cols, index=(cols.index(cfg.get("y")) + 1) if cfg.get("y") in cols else 0)
            cfg["color"] = st.selectbox("颜色/分组", options=[None] + cols, index=(cols.index(cfg.get("color")) + 1) if cfg.get("color") in cols else 0)
            cfg["size"] = st.selectbox("点大小（散点）", options=[None] + cols, index=(cols.index(cfg.get("size")) + 1) if cfg.get("size") in cols else 0)
            cfg["aggregate"] = st.selectbox("聚合函数（柱状）", options=["none", "sum", "mean", "median", "count", "min", "max"], index=["none", "sum", "mean", "median", "count", "min", "max"].index(cfg.get("aggregate", "none")))
            cfg["tooltip"] = st.multiselect("提示字段", options=cols, default=[cfg["x"], cfg["y"]] if cfg.get("x") in cols and cfg.get("y") in cols else [])
            cfg["sample_rows"] = st.number_input("采样行数（提升渲染速度）", min_value=100, max_value=200000, value=int(cfg.get("sample_rows", 5000)), step=100)

        with right:
            try:
                chart = altair_chart_from_config(df, cfg)
                st.altair_chart(chart, use_container_width=True)

                # 图表和数据下载功能
                st.markdown("##### 📥 下载选项")

                # 获取要下载的数据（优先使用清洗后的数据）
                clean_df = st.session_state.get("clean_df")
                raw_df = st.session_state.get("raw_df")

                if clean_df is not None and not clean_df.empty:
                    download_df = clean_df
                elif raw_df is not None and not raw_df.empty:
                    download_df = raw_df
                else:
                    download_df = None

                col_d1, col_d2, col_d3, col_d4 = st.columns(4)

                with col_d1:
                    # 下载为PNG（需要用户手动右键保存）
                    if st.button("💾 保存图表", help="点击后请在图表上右键选择'保存图片'", use_container_width=True):
                        st.info("💡 请在上方图表上右键点击，选择'保存图片'来下载PNG格式的图表")

                with col_d2:
                    # 下载图表数据为CSV
                    if download_df is not None and not download_df.empty:
                        chart_data = download_df
                        if cfg.get("sample_rows") and len(download_df) > cfg.get("sample_rows", 5000):
                            chart_data = download_df.sample(n=cfg.get("sample_rows", 5000), random_state=42)

                        csv_chart_data = chart_data.to_csv(index=False).encode("utf-8-sig")
                        st.download_button(
                            label="📊 图表数据CSV",
                            data=csv_chart_data,
                            file_name="chart_data.csv",
                            mime="text/csv",
                            help="下载当前图表使用的数据",
                            use_container_width=True
                        )

                with col_d3:
                    # 下载完整数据为CSV
                    if download_df is not None and not download_df.empty:
                        csv_full_data = download_df.to_csv(index=False).encode("utf-8-sig")
                        # 判断当前使用的是什么数据
                        if st.session_state.get("clean_df") is not None:
                            data_label = "📄 完整数据CSV"
                            file_name = "cleaned_data.csv"
                            help_text = "下载清洗后的完整数据"
                        else:
                            data_label = "📄 原始数据CSV"
                            file_name = "raw_data.csv"
                            help_text = "下载原始数据（未清洗）"

                        st.download_button(
                            label=data_label,
                            data=csv_full_data,
                            file_name=file_name,
                            mime="text/csv",
                            help=help_text,
                            use_container_width=True
                        )

                with col_d4:
                    # 下载完整数据为Excel
                    if download_df is not None and not download_df.empty:
                        excel_full_data = df_to_excel_bytes(download_df)
                        # 判断当前使用的是什么数据
                        if st.session_state.get("clean_df") is not None:
                            data_label = "📗 完整数据Excel"
                            file_name = "cleaned_data.xlsx"
                            help_text = "下载清洗后的完整数据"
                        else:
                            data_label = "📗 原始数据Excel"
                            file_name = "raw_data.xlsx"
                            help_text = "下载原始数据（未清洗）"

                        st.download_button(
                            label=data_label,
                            data=excel_full_data,
                            file_name=file_name,
                            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            help=help_text,
                            use_container_width=True
                        )

            except Exception as e:
                st.error(f"绘图失败：{e}")

        with st.expander("数据预览", expanded=False):
            st.dataframe(df.head(100), use_container_width=True)

# --------------------------
# Tab 4: 配置管理
# --------------------------
with tabs[3]:
    st.subheader("配置管理")

    st.info("💡 数据下载功能已移至【可视化】标签页，在那里可以下载图表、图表数据、完整CSV和Excel文件。")

    st.markdown("#### 📋 保存/加载分析配置")
    st.markdown("你可以保存当前的API配置、清洗代码和可视化设置，以便下次使用。")

    # 显示当前配置概览
    with st.expander("📖 查看当前配置概览", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**数据源配置**")
            source_type = st.session_state.get("source_type", "未设置")
            st.write(f"- 数据源类型: {source_type}")

            if source_type == "api":
                api_url = st.session_state.get("api_config", {}).get("url", "未设置")
                st.write(f"- API URL: {api_url[:50]}..." if len(api_url) > 50 else f"- API URL: {api_url}")
            elif source_type == "notion":
                notion_db_id = st.session_state.get("notion_config", {}).get("database_id", "未设置")
                st.write(f"- Notion DB: {notion_db_id[:20]}..." if len(notion_db_id) > 20 else f"- Notion DB: {notion_db_id}")

            clean_code = st.session_state.get("clean_code", "")
            st.write(f"- 清洗代码: {'已设置' if clean_code.strip() else '未设置'}")

        with col2:
            st.markdown("**数据状态**")
            raw_df = st.session_state.get("raw_df")
            clean_df = st.session_state.get("clean_df")

            if raw_df is not None:
                st.write(f"- 原始数据: {len(raw_df):,} 行 × {len(raw_df.columns)} 列")
            else:
                st.write("- 原始数据: 未导入")

            if clean_df is not None:
                st.write(f"- 清洗数据: {len(clean_df):,} 行 × {len(clean_df.columns)} 列")
            else:
                st.write("- 清洗数据: 未生成")

    # 配置保存和加载
    col_save, col_load = st.columns(2)

    with col_save:
        st.markdown("##### 💾 保存配置")
        cfg_bundle = {
            "api_config": st.session_state.get("api_config", {}),
            "notion_config": st.session_state.get("notion_config", {}),
            "clean_code": st.session_state.get("clean_code", ""),
            "viz_config": st.session_state.get("viz_config", {}),
        }
        cfg_json = json.dumps(cfg_bundle, ensure_ascii=False, indent=2)
        st.download_button(
            "📥 下载配置文件",
            data=cfg_json.encode("utf-8"),
            file_name="analysis_config.json",
            mime="application/json",
            use_container_width=True,
            help="保存当前的API配置、清洗代码和可视化设置"
        )

    with col_load:
        st.markdown("##### 📂 加载配置")
        uploaded_cfg = st.file_uploader(
            "选择配置文件",
            type=["json"],
            key="cfg_uploader",
            help="上传之前保存的配置文件来恢复设置"
        )

        if uploaded_cfg is not None:
            try:
                loaded = json.load(uploaded_cfg)
                if "api_config" in loaded:
                    st.session_state["api_config"] = {**st.session_state["api_config"], **loaded["api_config"]}
                if "notion_config" in loaded:
                    st.session_state["notion_config"] = {**st.session_state["notion_config"], **loaded["notion_config"]}
                if "clean_code" in loaded:
                    st.session_state["clean_code"] = loaded["clean_code"] or ""
                if "viz_config" in loaded:
                    st.session_state["viz_config"] = {**st.session_state["viz_config"], **loaded["viz_config"]}
                st.success("✅ 配置已加载成功！请根据需要重新拉取数据或运行清洗代码。")
            except Exception as e:
                st.error(f"❌ 加载配置失败：{e}")

    # 重置功能
    st.markdown("---")
    st.markdown("##### 🔄 重置选项")

    reset_col1, reset_col2, reset_col3 = st.columns(3)

    with reset_col1:
        if st.button("🗑️ 清空数据", help="清除所有导入的数据"):
            st.session_state["raw_df"] = None
            st.session_state["clean_df"] = None
            st.success("数据已清空")

    with reset_col2:
        if st.button("🧹 重置清洗", help="清空清洗代码和清洗结果"):
            st.session_state["clean_code"] = ""
            st.session_state["clean_df"] = None
            st.success("清洗设置已重置")

    with reset_col3:
        if st.button("⚠️ 重置全部", help="重置所有配置和数据", type="secondary"):
            # 重置所有session state
            for key in list(st.session_state.keys()):
                if key not in ["cfg_uploader"]:  # 保留文件上传器状态
                    del st.session_state[key]
            init_session_state()
            st.success("所有设置已重置")
            st.rerun()