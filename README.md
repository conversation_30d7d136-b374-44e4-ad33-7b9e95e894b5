# 数据分析工作台

一个基于 Streamlit 的数据分析工具，支持 CSV/Excel/API 数据导入、数据清洗和可视化。

## 功能特性

### 📊 数据导入
- **本地文件**: 支持 CSV 和 Excel 文件上传
- **API 接口**: 支持 REST API 数据拉取，包含认证和分页功能
- **编码自动检测**: CSV 文件编码问题自动处理

### 🛠️ 数据清洗
- **常用清洗操作**:
  - 删除空格（前后空格、所有空格、仅前空格、仅后空格）
  - 删除重复行（可指定列）
  - 删除空值行（可指定列和条件）
  - 数据类型转换（文本、整数、小数、日期）
  - 列重命名
- **自定义清洗**: 支持 Python/Pandas 代码自定义清洗逻辑
- **用户扩展**: 预留自定义清洗按钮区域（开发中）

### 📈 数据可视化
- **图表类型**: 折线图、柱状图、散点图、直方图、箱线图
- **交互配置**: 支持 X/Y 轴、颜色分组、点大小、提示字段等配置
- **聚合功能**: 支持 sum、mean、median、count、min、max 聚合
- **性能优化**: 大数据集自动采样提升渲染速度

### 📥 数据下载（集成在可视化界面）
- **图表下载**: 右键保存图表为图片
- **图表数据**: 下载当前图表使用的数据（CSV格式）
- **完整数据**: 下载清洗后的完整数据（CSV和Excel格式）
- **智能识别**: 自动使用清洗后数据，如无则使用原始数据

### ⚙️ 配置管理
- **配置保存**: 保存 API 配置、清洗代码、可视化设置
- **配置加载**: 加载之前保存的配置文件
- **状态概览**: 查看当前数据和配置状态
- **重置选项**: 清空数据、重置清洗、重置全部

## 安装和运行

```bash
# 安装依赖
pip install streamlit altair pandas openpyxl requests

# 运行应用
streamlit run data_board.py
```

## 使用示例

1. **导入数据**: 在"数据导入"标签页上传 CSV/Excel 文件或配置 API
2. **清洗数据**: 在"设置/清洗"标签页使用常用清洗按钮或编写自定义代码
3. **可视化**: 在"可视化"标签页配置图表参数并查看结果
4. **下载**: 在可视化界面直接下载图表、数据文件
5. **保存配置**: 在"配置管理"标签页保存当前设置以便复用

## 测试数据

项目包含 `test_data.csv` 测试文件，包含一些故意的数据质量问题（空格、空值等），可用于测试清洗功能。

## 技术栈

- **前端**: Streamlit
- **数据处理**: Pandas, NumPy
- **可视化**: Altair
- **文件处理**: openpyxl
- **网络请求**: requests
